# Midjourney任务状态轮询重构文档

## 概述

本次重构将Midjourney任务状态轮询从异步线程模式改为RocketMQ延时队列模式，以解决高并发时的资源紧张问题。

## 修改内容

### 1. 新增文件

#### 1.1 MjTaskPollingVo.java
- 路径：`src/main/java/com/lx/pl/dto/mq/MjTaskPollingVo.java`
- 功能：MJ任务状态轮询的消息体类
- 包含字段：jobId、loginName、currentAttempt、maxAttempts、pollingInterval、createTimestamp

#### 1.2 MjTaskPollingListener.java
- 路径：`src/main/java/com/lx/pl/mq/consumer/MjTaskPollingListener.java`
- 功能：MJ任务状态轮询的RocketMQ消费者
- 消费线程数：4个

### 2. 修改文件

#### 2.1 NormalMessageProducer.java
- 新增方法：`syncSendDelayTimeSeconds()` - 发送秒级延时消息
- 新增方法：`syncSendDelayTimeMills()` - 发送毫秒级延时消息

#### 2.2 MidjourneyService.java
- 修改方法：`startTaskStatusPolling()` - 从异步线程改为发送延时消息
- 新增方法：`handleTaskStatusPolling()` - 处理轮询消息的业务逻辑
- 新增方法：`sendPollingMessage()` - 发送轮询延时消息
- 新增方法：`scheduleNextPolling()` - 安排下一次轮询
- 新增常量：`MJ_TASK_POLLING_IDEMPOTENT_PREFIX` - 轮询幂等性前缀

#### 2.3 MidjourneyCallbackService.java
- 修改方法：`handleCallback()` - 增加幂等性处理
- 新增常量：`MJ_CALLBACK_IDEMPOTENT_PREFIX` - 回调幂等性前缀

### 3. 配置文件修改

在所有环境的配置文件中添加MJ轮询队列配置：
- `rocketmq.midjourney.polling.topic`
- `rocketmq.midjourney.polling.group`
- `rocketmq.midjourney.polling.tag`

环境配置：
- dev: `tp_midjourney_polling_dev`
- test: `tp_midjourney_polling_test`
- staging: `tp_midjourney_polling_stg`
- prestg: `tp_midjourney_polling_prestg`
- prod: `tp_midjourney_polling_prod`

## 技术特性

### 1. 幂等性保证
- **轮询幂等性**：使用Redis键 `mj:polling:idempotent:{jobId}_{attempt}` 防止重复处理
- **回调幂等性**：使用Redis键 `mj:callback:idempotent:{jobId}_{status}` 防止重复回调

### 2. 资源优化
- **消除异步线程**：不再使用CompletableFuture.runAsync()创建大量线程
- **延时队列**：使用RocketMQ延时消息机制，减少系统资源占用
- **可控并发**：通过消费者线程数控制并发处理能力

### 3. 容错机制
- **任务检查**：轮询前检查任务是否仍存在，避免处理已取消的任务
- **回调兼容**：保持与原有回调机制的兼容性
- **超时处理**：达到最大轮询次数后自动处理超时任务

## 工作流程

### 1. 任务创建流程
1. 调用`imagine()`方法创建MJ任务
2. 保存任务状态到Redis
3. 调用`startTaskStatusPolling()`启动轮询
4. 发送第一个延时消息（延时2秒）

### 2. 轮询处理流程
1. MQ消费者接收轮询消息
2. 执行幂等性检查
3. 检查任务是否仍然存在
4. 调用MJ API查询任务状态
5. 根据状态决定：
   - 成功/失败：处理结果，结束轮询
   - 进行中：更新Redis状态，发送下一个延时消息
   - 超时：标记任务失败

### 3. 回调处理流程
1. 接收MJ API回调
2. 执行幂等性检查
3. 根据状态处理任务结果
4. 更新Redis状态

## 优势

1. **资源节约**：避免创建大量长时间运行的异步线程
2. **高可用性**：利用RocketMQ的可靠性保证消息不丢失
3. **可扩展性**：可以通过增加消费者实例来提高处理能力
4. **监控友好**：可以通过RocketMQ控制台监控消息处理情况
5. **幂等安全**：双重幂等性保证，避免重复处理

## 注意事项

1. 需要确保RocketMQ支持延时消息功能
2. Redis中的幂等性键会自动过期，避免内存泄漏
3. 保持与现有回调机制的兼容性
4. 轮询间隔和最大次数可通过配置文件调整

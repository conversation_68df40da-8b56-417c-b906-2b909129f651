package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;

/**
 * 模型信息对象 gpt_model_about
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("gpt_model_about")
public class GptModelAbout extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 模型id */
    private String modelId;

    /** 模型名称 */
    private String modelDisplay;

    /** 模型类型 */
    private String modelType;

    /** 默认超分降噪指数 */
    private Double defaultHdFixDenoise;

    /** 模型图标 */
    private String modelAvatar;

    /** 模型描述 */
    private String modelDesc;

    /** 模型排序 */
    private Integer modelOrder;

    /** web,ios,android */
    private String platform;


    private Integer modelOriginType;

    /** 模型详情 defaultConfig 信息（JSON） */
    private String defaultConfig;

    /** 模型所支持的风格类型列表（JSON） */
    private String supportStyleList;

    private Integer baseCost;

}

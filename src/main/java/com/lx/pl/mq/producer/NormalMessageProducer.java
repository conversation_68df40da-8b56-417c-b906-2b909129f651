package com.lx.pl.mq.producer;

import com.lx.pl.mq.message.CommonMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.apache.rocketmq.client.support.RocketMQUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description: 消息生产者
 *
 * <AUTHOR>
 */
@Service
public class NormalMessageProducer<T> {
    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private RocketMQClientTemplate rocketMQClientTemplate;

    /**
     * 同步发送
     *
     * @param commonMqMessage 消息内容
     */
    public void syncSend(CommonMqMessage<T> commonMqMessage) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.send(destination, msg);
        logger.info("syncSend to topic: {} msgKey: {}", commonMqMessage.topic(), commonMqMessage.getMessageKey());
    }

    //延时队列
    public void delaySend(CommonMqMessage<T> commonMqMessage, long delayTime, CompletableFuture<SendReceipt> future0) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.asyncSendDelayMessage(destination, msg, Duration.ofSeconds(delayTime), future0);
        logger.info("delaySend to topic: {} msgKey: {}", commonMqMessage.topic(), commonMqMessage.getMessageKey());
    }

    /**
     * 异步发送消息
     *
     * @param commonMqMessage 消息内容
     */
    public void asyncSend(CommonMqMessage<T> commonMqMessage, CompletableFuture<SendReceipt> future0) {
        // springboot不支持使用header传递tags，根据要求，需要在topic后进行拼接 formats: `topicName:tags`，不拼接标识无tag
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.asyncSendNormalMessage(destination, msg, future0);
    }

    /**
     * 发送延时消息（秒级延时）
     *
     * @param commonMqMessage 消息内容
     * @param delayTimeSeconds 延时时间（秒）
     */
    public void syncSendDelayTimeSeconds(CommonMqMessage<T> commonMqMessage, long delayTimeSeconds) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.syncSendDelayTimeSeconds(destination, msg, delayTimeSeconds);
        logger.info("syncSendDelayTimeSeconds to topic: {} msgKey: {} delaySeconds: {}",
                commonMqMessage.topic(), commonMqMessage.getMessageKey(), delayTimeSeconds);
    }

    /**
     * 发送延时消息（毫秒级延时）
     *
     * @param commonMqMessage 消息内容
     * @param delayTimeMillis 延时时间（毫秒）
     */
    public void syncSendDelayTimeMills(CommonMqMessage<T> commonMqMessage, long delayTimeMillis) {
        String destination = commonMqMessage.destination();
        Message<T> msg = buildMessage(commonMqMessage);
        rocketMQClientTemplate.syncSendDelayTimeMills(destination, msg, delayTimeMillis);
        logger.info("syncSendDelayTimeMills to topic: {} msgKey: {} delayMillis: {}",
                commonMqMessage.topic(), commonMqMessage.getMessageKey(), delayTimeMillis);
    }


    private static <T> Message<T> buildMessage(CommonMqMessage<T> message) {
        String keys = RocketMQUtil.toRocketHeaderKey("KEYS");
        // object消息类型
        Message<T> msg = MessageBuilder.withPayload(message.getMessage())
                .setHeader(keys, message.getMessageKey())
                .build();
        return msg;
    }

}
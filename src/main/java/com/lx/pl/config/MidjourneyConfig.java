package com.lx.pl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Midjourney API配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "midjourney.api")
public class MidjourneyConfig {

    /**
     * TT API的基础URL
     */
    private String baseUrl = "https://api.ttapi.io";

    /**
     * TT API密钥
     */
    private String apiKey;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeout = 300;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 是否启用翻译
     */
    private Boolean translationEnabled = true;

    /**
     * 默认模式（fast/relax/turbo）
     */
    private String defaultMode = "fast";

    /**
     * 是否启用Prompt检查（全局开关）
     */
    private Boolean promptCheckEnabled = true;

    /**
     * 最大并发任务数（默认10个）
     */
    private Integer maxConcurrentJobs = 10;

    /**
     * 任务状态轮询间隔（秒，默认2秒）
     */
    private Integer pollingIntervalSeconds = 2;

    /**
     * 任务状态轮询最大次数（默认90次，即3分钟）
     */
    private Integer maxPollingAttempts = 90;

    /**
     * 余额告警阈值
     */
    private Integer balanceAlarmThreshold = 10000;
}
